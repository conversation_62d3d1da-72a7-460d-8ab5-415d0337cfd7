import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma, TagType } from '@prisma/client';
import { normalizeTags } from '@/common/utils/serialize';

import slugify from 'slugify';

type ArticleServiceOptions = {
  include?: Prisma.ArticleInclude;
  omit?: Prisma.ArticleOmit;
};

const defaultInclude: Prisma.ArticleInclude = { category: true, tags: true };

class ArticlesService {
  async create(
    data: {
      authorId: number;
      title: string;
      summary: string;
      categoryId: number;
      content?: string;
      thumbnailKey: string;
      redirectUrl?: string;
      tags?: string[];
    },
    options: ArticleServiceOptions = {},
  ) {
    const { title, tags = [], ...articleData } = data;
    const { include = defaultInclude, omit } = options;

    let slug = slugify(title);
    const article = await prisma.article.findUnique({ where: { slug } });
    if (article) slug = slug + '-' + Date.now().toString().slice(-4);

    return await prisma.article.create({
      include,
      omit,
      data: {
        title,
        slug,
        ...articleData,
        tags: {
          connectOrCreate: normalizeTags(tags).map((name) => ({
            where: { name_type: { name, type: TagType.ARTICLE } },
            create: { name, type: TagType.ARTICLE },
          })),
        },
      },
    });
  }

  async updateById(
    id: number,
    data: {
      title?: string;
      content?: string;
      thumbnailKey?: string;
      summary?: string;
      redirectUrl?: string;
      categoryId?: number;
      tags?: string[];
    },
    options: ArticleServiceOptions = {},
  ) {
    const { title, tags = [], ...articleData } = data;
    const { include = defaultInclude, omit } = options;

    let slug = title ? slugify(title) : undefined;

    if (slug) {
      const article = await prisma.article.findMany({ where: { slug, id: { notIn: [id] } } });
      if (article.length > 0) {
        slug = slug + '-' + Date.now().toString().slice(-4);
      }
    }

    return await withTransaction(async (tx) => {
      return await tx.article.update({
        include,
        omit,
        where: { id },
        data: {
          ...articleData,
          slug,
          tags:
            tags.length > 0
              ? {
                  set: [],
                  connectOrCreate: normalizeTags(tags).map((name) => ({
                    where: { name_type: { name, type: TagType.ARTICLE } },
                    create: { name, type: TagType.ARTICLE },
                  })),
                }
              : undefined,
        },
      });
    });
  }

  async getBySlug(slug: string, options: ArticleServiceOptions = {}) {
    const { include = defaultInclude, omit } = options;

    const article = withTransaction(async (tx) => {
      return await tx.article.update({
        include,
        omit,
        where: { slug },
        data: { views: { increment: 1 } },
      });
    });

    return article;
  }

  async like(id: number, options: ArticleServiceOptions = {}) {
    const { include = defaultInclude, omit } = options;
    return await prisma.article.update({
      include,
      omit,
      where: { id },
      data: { likes: { increment: 1 } },
    });
  }

  async share(id: number, options: ArticleServiceOptions = {}) {
    const { include = defaultInclude, omit } = options;
    return await prisma.article.update({
      include,
      omit,
      where: { id },
      data: { shares: { increment: 1 } },
    });
  }

  async getById(id: number, options: ArticleServiceOptions = {}) {
    const { include = defaultInclude, omit } = options;
    const article = await prisma.article.findUnique({ include, omit, where: { id } });
    return article;
  }

  async getList(
    query: {
      limit?: number;
      page?: number;
      search?: string;
      authorId?: number;
      categoryId?: number;
    },
    options: ArticleServiceOptions = {},
  ) {
    const { limit = 10, page = 1, search, authorId, categoryId } = query;
    const { include = defaultInclude, omit } = options;

    const where: Prisma.ArticleWhereInput = {};
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { summary: { contains: search, mode: 'insensitive' } },
        { slug: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (authorId) {
      where.authorId = authorId;
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    const [articles, count] = await prisma.$transaction([
      prisma.article.findMany({
        include,
        omit,
        take: limit,
        skip: (page - 1) * limit,
        where,
      }),
      prisma.article.count({ where }),
    ]);

    return { articles, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.article.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  async createCategory(data: { name: string }) {
    return await prisma.category.create({ data });
  }

  async getCategories() {
    return await prisma.category.findMany();
  }

  async getCategoryById(id: number) {
    return await prisma.category.findUnique({ where: { id } });
  }

  async updateCategoryById(id: number, data: { name: string }) {
    return await prisma.category.update({
      where: { id },
      data,
    });
  }

  async deleteCategoryById(id: number) {
    return await prisma.category.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  async getTrending(options: ArticleServiceOptions = {}) {
    const { include = defaultInclude, omit } = options;
    return await prisma.article.findFirst({
      include,
      omit,
      orderBy: { likes: 'desc', views: 'desc', createdAt: 'desc' },
    });
  }

  async unlike(id: number, options: ArticleServiceOptions = {}) {
    const { include = defaultInclude, omit } = options;
    return await prisma.article.update({
      include,
      omit,
      where: { id },
      data: { likes: { decrement: 1 } },
    });
  }
}

export default new ArticlesService();
