generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  USER
  ADMIN
  EMPLOYER
}

model Person {
  id            Int            @id @default(autoincrement())
  name          String
  email         String         @unique
  emailVerified DateTime?
  password      String
  refSource     String?
  active        Boolean        @default(true)
  // 
  role          Role           @default(USER)
  accounts      Account[]
  profile       Profile?
  employer      Employer?
  applications  Application[]
  notifications Notification[]
  articles      Article[]
  subscriptions Subscription[]
  // 
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  deletedAt     DateTime?
}

enum AccountType {
  OAUTH
  CREDENTIALS
}

enum Provider {
  GOOGLE
}

model Account {
  personId          Int
  person            Person      @relation(fields: [personId], references: [id])
  type              AccountType
  provider          Provider
  providerAccountId String
  refreshToken      String?
  accessToken       String?
  expiresAt         Int?
  tokenType         String?
  scope             String?
  idToken           String?
  //
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  deletedAt         DateTime?

  @@id([provider, providerAccountId])
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

model Profile {
  personId         Int       @unique
  person           Person    @relation(fields: [personId], references: [id])
  avatarKey        String?
  address          String?   @db.Text
  dob              DateTime?
  gender           Gender    @default(OTHER)
  resumeKey        String?
  completedAt      DateTime?
  // https://forms.gle/4A6JTCVyUodvmRq76
  givenName        String?
  familyName       String?
  phone            String?
  city             String?
  country          String?
  expertiseDomains String[]
  frenchLevel      String?
  otherLanguages   String[]
  experience       String?
  education        String?
  livedFrance      String?
  jobAvailability  String?
  geoMobility      String?
  //
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  deletedAt        DateTime?
}

model Employer {
  person    Person    @relation(fields: [personId], references: [id])
  personId  Int       @unique
  company   Company   @relation(fields: [companyId], references: [id])
  companyId Int
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model CompanyType {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  companies Company[]
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model Company {
  id            Int         @id @default(autoincrement())
  name          String
  address       String      @db.Text
  description   String?     @db.Text
  website       String?
  logoKey       String?
  videoUrl      String?
  services      String[]    @db.Text
  highlights    String[]    @db.Text
  jobs          Job[]
  employers     Employer[]
  companyTypeId Int
  companyType   CompanyType @relation(fields: [companyTypeId], references: [id])
  // 
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  deletedAt     DateTime?
}

enum TagType {
  JOB
  QUIZ
  ARTICLE
}

model Tag {
  id        Int       @id @default(autoincrement())
  name      String
  type      TagType
  jobs      Job[]
  quizzes   Quiz[]
  articles  Article[]
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  @@unique([name, type])
}

model Job {
  id           Int           @id @default(autoincrement())
  title        String        @db.Text
  description  String        @db.Text
  benefits     String[]      @db.Text
  requirements String[]      @db.Text
  salary       String?       @db.Text
  company      Company       @relation(fields: [companyId], references: [id])
  companyId    Int
  applications Application[]
  tags         Tag[]
  //
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  deletedAt    DateTime?
}

model Application {
  id        Int       @id @default(autoincrement())
  person    Person    @relation(fields: [personId], references: [id])
  personId  Int
  job       Job       @relation(fields: [jobId], references: [id])
  jobId     Int
  appliedAt DateTime  @default(now())
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model Notification {
  id        Int       @id @default(autoincrement())
  person    Person    @relation(fields: [personId], references: [id])
  personId  Int
  message   String    @db.Text
  read      Boolean   @default(false)
  url       String?
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model Category {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  articles  Article[]
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model Article {
  id           Int       @id @default(autoincrement())
  title        String    @db.Text
  content      String?   @db.Text
  summary      String?   @db.Text
  slug         String    @unique
  thumbnailKey String
  redirectUrl  String?
  author       Person    @relation(fields: [authorId], references: [id])
  authorId     Int
  category     Category  @relation(fields: [categoryId], references: [id])
  categoryId   Int
  tags         Tag[]
  views        Int       @default(0)
  likes        Int       @default(0)
  shares       Int       @default(0)
  //
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?
}

model SchoolType {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  schools   School[]
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model School {
  id           Int        @id @default(autoincrement())
  name         String     @unique
  city         String?
  address      String?    @db.Text
  description  String?    @db.Text
  logoKey      String?
  website      String?
  latitude     Float?
  longitude    Float?
  schoolType   SchoolType @relation(fields: [schoolTypeId], references: [id])
  schoolTypeId Int
  //
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  deletedAt    DateTime?
}

model Quiz {
  id          Int       @id @default(autoincrement())
  week        Int
  title       String    @db.Text
  description String    @db.Text
  kahootUrl   String
  imageKey    String?
  tags        Tag[]
  //
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
}

model Challenge {
  id           Int       @id @default(autoincrement())
  title        String    @db.Text
  description  String    @db.Text
  thumbnailKey String
  articleUrl   String?
  //
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?
}

model Subscription {
  id             Int       @id @default(autoincrement())
  personId       Int?
  person         Person?   @relation(fields: [personId], references: [id])
  endpoint       String    @unique
  expirationTime DateTime?
  p256dh         String
  auth           String
  //
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?
}

model EmailSubscribe {
  id        Int       @id @default(autoincrement())
  email     String    @unique
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model ContactUs {
  id        Int       @id @default(autoincrement())
  name      String
  phone     String
  email     String?
  address   String
  message   String    @db.Text
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model EmailVerified {
  id         Int       @id @default(autoincrement())
  email      String
  token      String    @unique
  sendedAt   DateTime
  expiredAt  DateTime
  verifiedAt DateTime?
  //
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime?
}
